<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试原生Select样式</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #4096ff;
        }
        .demo-container {
            padding: 20px;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            background: #fafafa;
            margin: 15px 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background: white;
        }
        .comparison-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>原生Select样式测试</h1>
    
    <div class="instructions">
        <h2>测试说明</h2>
        <p>这个页面用于测试修改后的LanguageSelector组件，现在使用原生HTML select元素并应用了类似Ant Design的样式。</p>
        <ol>
            <li>确保Chrome扩展已经重新构建并加载</li>
            <li>在下面的测试文本上选择文字</li>
            <li>观察弹出的语言选择器是否使用了新的原生select样式</li>
            <li>测试下拉选择功能是否正常工作</li>
        </ol>
    </div>

    <div class="test-section">
        <div class="test-title">样式对比演示</div>
        <div class="comparison">
            <div class="comparison-item">
                <div class="comparison-title">期望的样式效果</div>
                <div style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; background: #ffffff; border-radius: 6px; border: 1px solid #d9d9d9; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);">
                    <select style="appearance: none; flex: 1; min-width: 80px; height: 32px; padding: 4px 24px 4px 11px; font-size: 14px; color: rgba(0, 0, 0, 0.88); background-color: #ffffff; border: 1px solid #d9d9d9; border-radius: 6px; outline: none; cursor: pointer; background-image: url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'none\' viewBox=\'0 0 20 20\'%3e%3cpath stroke=\'%236b7280\' stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'1.5\' d=\'M6 8l4 4 4-4\'/%3e%3c/svg%3e'); background-position: right 8px center; background-repeat: no-repeat; background-size: 16px;">
                        <option value="auto">自动检测</option>
                        <option value="en">英语</option>
                        <option value="zh">中文</option>
                    </select>
                    <div style="color: rgba(0, 0, 0, 0.45); font-size: 12px; margin: 0 4px;">→</div>
                    <select style="appearance: none; flex: 1; min-width: 80px; height: 32px; padding: 4px 24px 4px 11px; font-size: 14px; color: rgba(0, 0, 0, 0.88); background-color: #ffffff; border: 1px solid #d9d9d9; border-radius: 6px; outline: none; cursor: pointer; background-image: url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'none\' viewBox=\'0 0 20 20\'%3e%3cpath stroke=\'%236b7280\' stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'1.5\' d=\'M6 8l4 4 4-4\'/%3e%3c/svg%3e'); background-position: right 8px center; background-repeat: no-repeat; background-size: 16px;">
                        <option value="zh">中文</option>
                        <option value="en">英语</option>
                        <option value="ja">日语</option>
                    </select>
                </div>
            </div>
            <div class="comparison-item">
                <div class="comparison-title">实际扩展效果</div>
                <p>请在下面的测试文本中选择文字来查看实际效果</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">测试文本区域</div>
        <div class="demo-container">
            <p><strong>测试文本 1：</strong>这是一段中文测试文本，请选择这段文字来触发语言选择器。新的原生select应该有清晰的边框、正确的字体和适当的间距，看起来类似Ant Design的Select组件。</p>
        </div>
        
        <div class="demo-container">
            <p><strong>Test Text 2:</strong> This is an English test paragraph. Please select this text to trigger the language selector. The new native select should have clean borders, proper fonts, and appropriate spacing that resembles Ant Design's Select component.</p>
        </div>
        
        <div class="demo-container">
            <p><strong>测试文本 3：</strong>这是一个更长的测试段落，用于测试在不同长度的文本选择情况下，原生select组件的样式表现。Lorem ipsum dolor sit amet, consectetur adipiscing elit. 新的样式应该保持一致性和良好的用户体验。</p>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">样式检查清单</div>
        <ul>
            <li>✓ Select元素有正确的边框和背景色（白色背景，灰色边框）</li>
            <li>✓ 自定义下拉箭头正确显示（替代了浏览器默认箭头）</li>
            <li>✓ 字体大小和颜色符合设计规范（14px，深灰色文字）</li>
            <li>✓ 悬停效果正常工作（边框变蓝色）</li>
            <li>✓ 焦点效果正常工作（蓝色边框和阴影）</li>
            <li>✓ 选项列表样式正确</li>
            <li>✓ 组件在Shadow DOM中正确隔离</li>
            <li>✓ 没有与页面原有样式冲突</li>
            <li>✓ 响应式布局正常工作</li>
        </ul>
    </div>

    <script>
        console.log('原生Select测试页面已加载');
        
        // 检查扩展注入情况
        setTimeout(() => {
            const container = document.getElementById('web-assistant-main-container');
            if (container && container.shadowRoot) {
                console.log('✓ Chrome扩展已注入，Shadow DOM已创建');
                
                // 检查样式
                const styles = container.shadowRoot.querySelectorAll('style');
                console.log(`发现 ${styles.length} 个样式元素`);
                
                // 查找LanguageSelector相关样式
                styles.forEach((style, index) => {
                    if (style.textContent.includes('selectInput') || style.textContent.includes('languageSelector')) {
                        console.log(`✓ 找到LanguageSelector样式 (样式元素 ${index + 1})`);
                    }
                });
            } else {
                console.warn('⚠ Chrome扩展未正确注入');
            }
        }, 2000);
        
        // 添加一些交互提示
        document.addEventListener('mouseup', () => {
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                console.log('文本已选择:', selection.toString().substring(0, 50) + '...');
                console.log('等待语言选择器出现...');
            }
        });
    </script>
</body>
</html>
